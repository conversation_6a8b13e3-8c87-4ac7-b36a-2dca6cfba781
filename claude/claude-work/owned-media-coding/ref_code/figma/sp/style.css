.box {
  position: relative;
  width: 375px;
  height: 2704px;
}

.box .s {
  position: fixed;
  width: 375px;
  height: 2704px;
  top: 0;
  left: 0;
  background-color: #ffffff;
}

.box .view {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  position: absolute;
  top: 80px;
  left: 87px;
}

.box .text-wrapper {
  width: fit-content;
  font-weight: 700;
  color: #3ab795;
  font-size: 28px;
  text-align: center;
  letter-spacing: 0.28px;
  line-height: normal;
  position: relative;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
}

.box .frame {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  position: relative;
  flex: 0 0 auto;
}

.box .line {
  position: relative;
  width: 15px;
  height: 2px;
}

.box .div {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 500;
  color: #3ab795;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.20px;
  line-height: normal;
  white-space: nowrap;
}

.box .frame-2 {
  display: flex;
  flex-direction: column;
  width: 335px;
  align-items: flex-start;
  gap: 35px;
  position: absolute;
  top: 224px;
  left: 20px;
}

.box .qn-a {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 35px;
  position: relative;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
}

.box .div-2 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
}

.box .overlap-group-wrapper {
  position: relative;
  width: 42px;
  height: 40px;
}

.box .overlap-group {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: #3ab795;
  border-radius: 7px;
}

.box .text-wrapper-2 {
  position: absolute;
  width: 20px;
  height: 30px;
  top: 2px;
  left: 10px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #ffffff;
  font-size: 28px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.box .text-wrapper-3 {
  width: 282px;
  font-weight: 700;
  color: #5f6061;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 35px;
  position: relative;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
}

.box .div-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: #8fbdc3;
  border-radius: 7px;
}

.box .text-wrapper-4 {
  position: absolute;
  width: 20px;
  height: 30px;
  top: 5px;
  left: 10px;
  font-family: "Noto Sans JP", Helvetica;
  font-weight: 900;
  color: #ffffff;
  font-size: 28px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.box .text-wrapper-5 {
  width: 282px;
  font-weight: 400;
  color: #333333;
  font-size: 18px;
  letter-spacing: 0.36px;
  line-height: 30px;
  position: relative;
  margin-top: -1.00px;
  font-family: "Noto Sans JP", Helvetica;
}

.box .img {
  position: relative;
  align-self: stretch;
  width: 100%;
  height: 1px;
  object-fit: cover;
}
